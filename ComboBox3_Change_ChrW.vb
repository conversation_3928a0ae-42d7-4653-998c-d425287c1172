Private Sub ComboBox3_Change()
    Me.TextBox12.Value = ""

    Select Case Me.ComboBox3.Value
        Case ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 1", "Level 1"
            Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E6").Value, "$#,##0.00")
        Case ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 2", "Level 2"
            Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E7").Value, "$#,##0.00")
        Case ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 3", "Level 3"
            Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E8").Value, "$#,##0.00")
        Case ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 4", "Level 4"
            Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E9").Value, "$#,##0.00")
        Case ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 5", "Level 5"
            Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E10").Value, "$#,##0.00")
    End Select

End Sub
