Private Sub CommandButton1_Click()
    Dim salary As Double
    Dim totalResult As Double
    Dim reportText As String  ' تعريف المتغير المفقود
    
    If Not IsNumeric(Replace(Me.TextBox10.Value, "$", "")) Then
        ShowArabicMessage CreateArabicText("select_rank"), CreateArabicText("warning_title"), 48
        Exit Sub
    End If
    If Trim(Me.TextBox11.Value) = "" Or Not IsNumeric(Replace(Me.TextBox11.Value, " Lot", "")) Then
        ShowArabicMessage CreateArabicText("enter_volume"), CreateArabicText("warning_title"), 48
        Me.TextBox9.Value = ""
        Exit Sub
    End If
    If CDbl(Replace(Me.TextBox11.Value, " Lot", "")) < 100 Then
        ShowArabicMessage CreateArabicText("no_salary"), CreateArabicText("error_title"), 16
        Me.TextBox9.Value = ""
        Exit Sub
    End If
    
    salary = CDbl(Replace(Me.TextBox10.Value, "$", ""))
    totalResult = 0
    
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("daily_post") & ": 30 / 30 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("withdrawal_proof") & ": 4 / 4") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("enzo_reasons") & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("live_webinar") & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.1)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("interactive_reels") & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.08)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("educational_courses") & ": 5 / 5 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("weekly_analysis") & ": 4 / 4 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("withdrawal_bonus") & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("company_cooperation") & ": 1 / 1 ") > 0 Then totalResult = totalResult + (salary * 0.1)
    
    Me.TextBox9.Value = Format(totalResult, "$#,##0.00")
    
    ' إنشاء نص التقرير المطلوب
    reportText = "الوكيل: " & vbCrLf & _
                 "نوع الوكالة: " & Me.ComboBox2.Value & vbCrLf & _
                 "مرتبة الوكيل: " & Me.ComboBox1.Value & vbCrLf & _
                 "الراتب المستحق للوكيل: " & Me.TextBox9.Value & vbCrLf & vbCrLf & _
                 Me.TextBox7.Value

    ' نسخ التقرير إلى الحافظة
    With New MSForms.DataObject
        .SetText reportText
        .PutInClipboard
    End With

    ' رسالة تأكيد
    ShowArabicMessage "تم نسخ التقرير بنجاح إلى الحافظة.", "تم بنجاح", 64
 
End Sub
