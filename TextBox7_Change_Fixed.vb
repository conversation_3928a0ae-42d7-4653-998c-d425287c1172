Private Sub TextBox7_Change()
    Dim regex As Object
    Dim matches As Object
    Dim agencyNumber As String
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim cell As Range
    Dim foundMatch As Boolean

    On Error GoTo CleanExit

    If Trim(Me.TextBox7.Value) = "" Then
        Me.ComboBox6.Value = ""
        Exit Sub
    End If

    Set regex = CreateObject("VBScript.RegExp")
    If regex Is Nothing Then
        MsgBox ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H643) & ChrW(&H648) & ChrW(&H646) & " RegExp " & ChrW(&H63A) & ChrW(&H64A) & ChrW(&H631) & " " & ChrW(&H645) & ChrW(&H62A) & ChrW(&H627) & ChrW(&H62D) & ".", vbExclamation, ChrW(&H62E) & ChrW(&H637) & ChrW(&H623)
        Exit Sub
    End If

    With regex
        .Pattern = ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & "\s*(\d+)"
        .IgnoreCase = True
        .Global = False
    End With

    If regex.Test(Me.TextBox7.Value) Then
        Set matches = regex.Execute(Me.TextBox7.Value)
        If Not matches Is Nothing Then
            If matches.Count > 0 Then
                agencyNumber = Trim(matches(0).SubMatches(0))
            End If
        End If
    Else
        Me.ComboBox6.Value = ""
        Exit Sub
    End If

    On Error Resume Next
    Set ws = ThisWorkbook.Sheets("Sheet3")
    On Error GoTo CleanExit
    If ws Is Nothing Then
        MsgBox ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H631) & ChrW(&H642) & ChrW(&H629) & " Sheet3 " & ChrW(&H63A) & ChrW(&H64A) & ChrW(&H631) & " " & ChrW(&H645) & ChrW(&H648) & ChrW(&H62C) & ChrW(&H648) & ChrW(&H62F) & ChrW(&H629) & ".", vbExclamation, ChrW(&H62E) & ChrW(&H637) & ChrW(&H623)
        Exit Sub
    End If
    
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    foundMatch = False

    For Each cell In ws.Range("A1:A" & lastRow)
        If Trim(CStr(cell.Value)) = agencyNumber Then
            foundMatch = True
            Exit For
        End If
    Next cell

    If foundMatch Then
        Me.ComboBox6.Value = agencyNumber
    Else
        Me.ComboBox6.Value = ""
    End If

CleanExit:
    On Error Resume Next
    Set matches = Nothing
    Set regex = Nothing
    Set ws = Nothing
End Sub
