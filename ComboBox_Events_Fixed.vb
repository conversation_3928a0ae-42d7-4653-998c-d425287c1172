Private Sub ComboBox1_Change()
    Me.TextBox10.Value = ""
    Me.TextBox12.Value = ""
    Me.TextBox13.Value = ""
    Me.TextBox8.Value = ""
        Select Case Me.ComboBox1.Value
        ' الوكالات الحصرية
        Case ChrW(&H628) & ChrW(&H631) & ChrW(&H648) & ChrW(&H646) & ChrW(&H632) & ChrW(&H64A), "Bronze":   Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B2").Value, "$#,##0.00")
        Case ChrW(&H641) & ChrW(&H636) & ChrW(&H64A), "Silver":   Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B3").Value, "$#,##0.00")
        Case ChrW(&H630) & ChrW(&H647) & ChrW(&H628) & ChrW(&H64A), "Gold":     Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B4").Value, "$#,##0.00")
        Case ChrW(&H628) & ChrW(&H644) & ChrW(&H627) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H646) & ChrW(&H64A), "Platinum": Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B5").Value, "$#,##0.00")
        Case ChrW(&H623) & ChrW(&H644) & ChrW(&H645) & ChrW(&H627) & ChrW(&H633), "Diamond":  Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B6").Value, "$#,##0.00")
        Case ChrW(&H632) & ChrW(&H641) & ChrW(&H64A) & ChrW(&H631), "Sapphire": Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B7").Value, "$#,##0.00")
        Case ChrW(&H632) & ChrW(&H645) & ChrW(&H631) & ChrW(&H62F), "Emerald":  Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B8").Value, "$#,##0.00")
        Case ChrW(&H645) & ChrW(&H644) & ChrW(&H643), "king":     Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B9").Value, "$#,##0.00")
        Case ChrW(&H627) & ChrW(&H644) & ChrW(&H623) & ChrW(&H633) & ChrW(&H637) & ChrW(&H648) & ChrW(&H631) & ChrW(&H629), "The Legend": Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B10").Value, "$#,##0.00")

        ' الوكالات الاعتيادية
        Case ChrW(&H628) & ChrW(&H62F) & ChrW(&H627) & ChrW(&H64A) & ChrW(&H629), "Beginning": Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E2").Value, "$#,##0.00")
        Case ChrW(&H646) & ChrW(&H645) & ChrW(&H648), "Growth":    Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E3").Value, "$#,##0.00")
        Case ChrW(&H645) & ChrW(&H62D) & ChrW(&H62A) & ChrW(&H631) & ChrW(&H641), "Pro"
            Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E4").Value, "$#,##0.00")
            Me.TextBox13.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E11").Value, "$#,##0.00")
        Case ChrW(&H646) & ChrW(&H62E) & ChrW(&H628) & ChrW(&H629), "Elite"
            Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E5").Value, "$#,##0.00")
            Me.TextBox13.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E12").Value, "$#,##0.00")
    End Select
    Select Case Me.ComboBox1.Value
        Case "Pro", "Elite"
            Me.TextBox13.Visible = True
            Me.Label11.Visible = True
        Case Else
            Me.TextBox13.Visible = False
            Me.Label11.Visible = False
    End Select
    If Me.ComboBox1.Value = "Elite" Then
        Me.Label12.Visible = True
        Me.ComboBox3.Visible = True
        Me.ComboBox3.Clear
        Me.ComboBox3.AddItem "level 1"
        Me.ComboBox3.AddItem "level 2"
        Me.ComboBox3.AddItem "level 3"
    Else
        Me.Label12.Visible = False
        Me.ComboBox3.Visible = False
    End If
End Sub

Private Sub ComboBox2_Change()
    On Error Resume Next
    Dim i As Long
    Dim optionsRegular As Variant
    Dim optionsExclusive As Variant
    ' النصوص العربية للوكالات الاعتيادية
    optionsRegular = Array(ChrW(&H628) & ChrW(&H62F) & ChrW(&H627) & ChrW(&H64A) & ChrW(&H629), _
                          ChrW(&H646) & ChrW(&H645) & ChrW(&H648), _
                          ChrW(&H645) & ChrW(&H62D) & ChrW(&H62A) & ChrW(&H631) & ChrW(&H641), _
                          ChrW(&H646) & ChrW(&H62E) & ChrW(&H628) & ChrW(&H629))
    ' النصوص العربية للوكالات الحصرية
    optionsExclusive = Array(ChrW(&H628) & ChrW(&H631) & ChrW(&H648) & ChrW(&H646) & ChrW(&H632) & ChrW(&H64A), _
                            ChrW(&H641) & ChrW(&H636) & ChrW(&H64A), _
                            ChrW(&H630) & ChrW(&H647) & ChrW(&H628) & ChrW(&H64A), _
                            ChrW(&H628) & ChrW(&H644) & ChrW(&H627) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H646) & ChrW(&H64A), _
                            ChrW(&H623) & ChrW(&H644) & ChrW(&H645) & ChrW(&H627) & ChrW(&H633), _
                            ChrW(&H632) & ChrW(&H641) & ChrW(&H64A) & ChrW(&H631), _
                            ChrW(&H632) & ChrW(&H645) & ChrW(&H631) & ChrW(&H62F), _
                            ChrW(&H645) & ChrW(&H644) & ChrW(&H643), _
                            ChrW(&H627) & ChrW(&H644) & ChrW(&H623) & ChrW(&H633) & ChrW(&H637) & ChrW(&H648) & ChrW(&H631) & ChrW(&H629))
    ComboBox1.Clear
    Me.TextBox10.Value = ""
    Me.TextBox12.Value = ""
    Me.TextBox13.Value = ""
    Me.TextBox8.Value = ""
    
    ' البحث عن النص العربي أو الإنجليزي
    Dim comboValue As String
    comboValue = LCase(Trim(ComboBox2.Value))
    
    Select Case comboValue
        Case ChrW(&H62D) & ChrW(&H635) & ChrW(&H631) & ChrW(&H64A), "exclusive", "حصري"
            For i = LBound(optionsExclusive) To UBound(optionsExclusive)
                ComboBox1.AddItem optionsExclusive(i)
            Next i
            ComboBox1.ListIndex = 0
            Label5.Visible = True
            TextBox10.Visible = True
            Label4.Visible = True
            TextBox9.Visible = True
            CommandButton1.Visible = True
            Label6.Visible = True
            TextBox11.Visible = True
            Label9.Visible = False
            TextBox12.Visible = False
            CommandButton2.Visible = False
            Label10.Visible = False
            TextBox8.Visible = False
            
        Case ChrW(&H625) & ChrW(&H639) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H62F) & ChrW(&H64A), "regular", "إعتيادي"
            For i = LBound(optionsRegular) To UBound(optionsRegular)
                ComboBox1.AddItem optionsRegular(i)
            Next i
            ComboBox1.ListIndex = 0
            TextBox11.Visible = True
            Label9.Visible = True
            TextBox12.Visible = True
            CommandButton2.Visible = True
            Label10.Visible = True
            TextBox8.Visible = True
            Label5.Visible = False
            TextBox10.Visible = False
            Label4.Visible = False
            TextBox9.Visible = False
            CommandButton1.Visible = False
            Label6.Visible = True
            TextBox10.Visible = False
            
        Case Else
            ComboBox1.Clear
            Label5.Visible = False
            TextBox10.Visible = False
            Label4.Visible = False
            TextBox9.Visible = False
            CommandButton1.Visible = False
            Label6.Visible = False
            TextBox11.Visible = False
            Label9.Visible = False
            TextBox12.Visible = False
            TextBox8.Visible = False
            CommandButton2.Visible = False
            Label10.Visible = False
            TextBox13.Visible = False
            Label12.Visible = False
    End Select
    
    On Error GoTo 0
End Sub
