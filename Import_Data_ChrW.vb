Private Sub ImportData_Click()
    Dim filePath As String
    Dim wbImport As Workbook
    Dim wsImport As Worksheet
    Dim wsTarget As Worksheet

    Set wsTarget = ThisWorkbook.Sheets("Sheet3")

    filePath = Application.GetOpenFilename("Excel / CSV Files (*.xls;*.xlsx;*.xlsm;*.csv), *.xls;*.xlsx;*.xlsm;*.csv", , ChrW(&H64A) & ChrW(&H631) & ChrW(&H62C) & ChrW(&H649) & " " & ChrW(&H627) & ChrW(&H62E) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H631) & " " & ChrW(&H645) & ChrW(&H644) & ChrW(&H641) & " Excel " & ChrW(&H623) & ChrW(&H648) & " CSV " & ChrW(&H644) & ChrW(&H644) & ChrW(&H627) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62F))

    If filePath = "False" Then Exit Sub

    If MsgBox(ChrW(&H647) & ChrW(&H644) & " " & ChrW(&H623) & ChrW(&H646) & ChrW(&H62A) & " " & ChrW(&H645) & ChrW(&H62A) & ChrW(&H623) & ChrW(&H643) & ChrW(&H62F) & " " & ChrW(&H623) & ChrW(&H646) & ChrW(&H643) & " " & ChrW(&H62A) & ChrW(&H631) & ChrW(&H64A) & ChrW(&H62F) & " " & ChrW(&H62D) & ChrW(&H630) & ChrW(&H641) & " " & ChrW(&H62C) & ChrW(&H645) & ChrW(&H64A) & ChrW(&H639) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H628) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H646) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H645) & ChrW(&H646) & " Sheet3 " & ChrW(&H648) & ChrW(&H627) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62F) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H628) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H646) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62C) & ChrW(&H62F) & ChrW(&H64A) & ChrW(&H62F) & ChrW(&H629) & ChrW(&H61F), _
              vbYesNo + vbQuestion, ChrW(&H62A) & ChrW(&H623) & ChrW(&H643) & ChrW(&H64A) & ChrW(&H62F) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H639) & ChrW(&H645) & ChrW(&H644) & ChrW(&H64A) & ChrW(&H629)) = vbNo Then Exit Sub

    Application.ScreenUpdating = False
    Application.DisplayAlerts = False

    wsTarget.Cells.Clear

    Set wbImport = Workbooks.Open(filePath)
    Set wsImport = wbImport.Sheets(1) ' الورقة الأولى من الملف المستورد

    wsImport.UsedRange.Copy Destination:=wsTarget.Range("A1")

    wbImport.Close SaveChanges:=False

    Application.DisplayAlerts = True
    Application.ScreenUpdating = True

    MsgBox ChrW(&H62A) & ChrW(&H645) & " " & ChrW(&H62D) & ChrW(&H630) & ChrW(&H641) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H628) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H646) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H642) & ChrW(&H62F) & ChrW(&H64A) & ChrW(&H645) & ChrW(&H629) & " " & ChrW(&H648) & ChrW(&H627) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62F) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H644) & ChrW(&H641) & " " & ChrW(&H628) & ChrW(&H646) & ChrW(&H62C) & ChrW(&H627) & ChrW(&H62D) & " " & ChrW(&H625) & ChrW(&H644) & ChrW(&H649) & " Sheet3", vbInformation, ChrW(&H639) & ChrW(&H645) & ChrW(&H644) & ChrW(&H64A) & ChrW(&H629) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H627) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62F)

End Sub
