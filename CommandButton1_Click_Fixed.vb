Private Sub CommandButton1_Click()
    Dim salary As Double
    Dim totalResult As Double
    Dim reportText As String
    Dim formattedSalary As String
    Dim fullText As String
    Dim lineText As String
    Dim agencyNumber As String
    Dim dateFrom As String
    Dim dateTo As String
    Dim posFind As Long, posEndLine As Long
    Dim arrowPos As Long
    Dim pattern As String
    Dim matches As Object, regex As Object

    ' التحقق من القيم المدخلة
    If Not IsNumeric(Replace(Me.TextBox10.Value, "$", "")) Then
        ShowArabicMessage CreateArabicText("select_rank"), CreateArabicText("warning_title"), 48
        Exit Sub
    End If

    If Trim(Me.TextBox11.Value) = "" Or Not IsNumeric(Replace(Me.TextBox11.Value, " Lot", "")) Then
        ShowArabicMessage CreateArabicText("enter_volume"), CreateArabicText("warning_title"), 48
        Me.TextBox9.Value = ""
        Exit Sub
    End If

    If CDbl(Replace(Me.TextBox11.Value, " Lot", "")) < 100 Then
        ShowArabicMessage CreateArabicText("no_salary"), CreateArabicText("error_title"), 16
        Me.TextBox9.Value = ""
        Exit Sub
    End If

    ' حساب الراتب المستحق
    salary = CDbl(Replace(Me.TextBox10.Value, "$", ""))
    totalResult = 0

    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("daily_post") & ": 30 / 30 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("withdrawal_proof") & ": 4 / 4") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("enzo_reasons") & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("live_webinar") & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.1)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("interactive_reels") & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.08)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("educational_courses") & ": 5 / 5 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("weekly_analysis") & ": 4 / 4 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("withdrawal_bonus") & ": 2 / 2 ") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(Me.TextBox7.Value, "- " & CreateArabicText("company_cooperation") & ": 1 / 1 ") > 0 Then totalResult = totalResult + (salary * 0.1)

    ' تنسيق المبلغ وإظهاره
    formattedSalary = Format(totalResult, "$#,##0.00")
    Me.TextBox9.Value = formattedSalary
    
    ' محاولة تعديل TextBox8 مع تجاهل الخطأ إن وجد
    On Error Resume Next
    Me.TextBox8.Value = formattedSalary
    On Error GoTo 0

    ' استخراج سطر تقرير الوكالة من النص الكامل
    fullText = Me.TextBox7.Value
    posFind = InStr(fullText, "تقرير وكالة")
    If posFind > 0 Then
        posEndLine = InStr(posFind, fullText, vbCrLf)
        If posEndLine = 0 Then posEndLine = Len(fullText) + 1
        lineText = Mid(fullText, posFind, posEndLine - posFind)
        lineText = Trim(lineText)
    Else
        lineText = ""
    End If

    ' تهيئة المتغيرات
    agencyNumber = ""
    dateFrom = ""
    dateTo = ""

    ' استخدام تعبير منتظم لاستخراج رقم الوكالة والتواريخ
    If lineText <> "" Then
        On Error Resume Next
        Set regex = CreateObject("VBScript.RegExp")
        If Not regex Is Nothing Then
            regex.Global = False
            regex.IgnoreCase = True
            ' النمط: تقرير وكالة [رقم] في المدة [تاريخ] ? [تاريخ]
            regex.Pattern = "تقرير وكالة\s+(\d+)\s+في المدة\s+(\d{4}-\d{2}-\d{2})\s*[^0-9]+(\d{4}-\d{2}-\d{2})"

            If regex.Test(lineText) Then
                Set matches = regex.Execute(lineText)
                If matches.Count > 0 Then
                    agencyNumber = matches(0).SubMatches(0)
                    dateFrom = matches(0).SubMatches(1)
                    dateTo = matches(0).SubMatches(2)
                End If
            End If
        End If
        On Error GoTo 0
    End If

    ' إنشاء نص التقرير النهائي
    reportText = "الوكيل:" & vbCrLf & _
                 "نوع الوكالة: " & Me.ComboBox2.Value & vbCrLf & _
                 "مرتبة الوكيل: " & Me.ComboBox1.Value & vbCrLf & _
                 "رقم الوكالة: " & agencyNumber & vbCrLf & _
                 "المدة من: " & dateFrom & vbCrLf & _
                 "إلى: " & dateTo & vbCrLf & _
                 "الراتب المستحق للوكيل: " & formattedSalary & vbCrLf & vbCrLf & _
                 Me.TextBox7.Value

    ' نسخ التقرير إلى الحافظة
    On Error Resume Next
    With New MSForms.DataObject
        .SetText reportText
        .PutInClipboard
    End With
    On Error GoTo 0

    ShowArabicMessage "تم نسخ التقرير بنجاح إلى الحافظة.", "تم بنجاح", 64
End Sub
