    ' كود إضافة العناصر إلى ComboBox3 مع تحويل النصوص العربية إلى ChrW
    
    If Me.ComboBox1.Value = "Elite" Then
        Me.Label12.Visible = True
        Me.ComboBox3.Visible = True
        Me.ComboBox3.Clear
        Me.ComboBox3.AddItem ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 1"  ' "المستوى 1"
        Me.ComboBox3.AddItem ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 2"  ' "المستوى 2"
        Me.ComboBox3.AddItem ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 3"  ' "المستوى 3"
     Else
        Me.Label12.Visible = False
        Me.ComboBox3.Visible = False
    End If
