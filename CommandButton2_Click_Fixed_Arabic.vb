Private Sub CommandButton2_Click()
    Dim txt As String
    Dim val5 As Double, val6 As Double
    Dim totalDaily As Double
    Dim totalWeekly As Double
    Dim totalValue As Double
    Dim selectedRank As String
    Dim inputValue As Double
    Dim resultValue As Double
    Dim multiplier As Long
    Dim val As String
    Dim Lines As Variant, Line As Variant
    Dim cleanedText As String
    Dim fullText As String
    Dim formattedDaily As String
    Dim formattedWeekly As String
    Dim formattedTotal As String
    Dim reportText As String
    Dim dailyBonusLine As String
    Dim weeklyBonusLine As String
    Dim volumeBonusLine As String
    Dim totalBonusLine As String
    Dim agencyNumber As String
    Dim totalAll As Double
    Dim bonusCount As Integer
    Dim numberedLines As String
    Dim formattedVolume As String
    Dim eliteLevel As String
    Dim eliteLevelText As String

    On Error Resume Next

    txt = Me.TextBox7.Value
    totalDaily = 0
    totalWeekly = 0
    totalValue = 0
    selectedRank = Me.ComboBox1.Value
    agencyNumber = Me.ComboBox6.Value

    If Trim(Me.TextBox12.Value) = "" Or Not IsNumeric(Replace(Me.TextBox12.Value, "$", "")) Then
        ShowArabicMessage CreateArabicText("select_daily_level"), CreateArabicText("error_title"), 16
        Me.TextBox12.SetFocus
        Exit Sub
    End If

    val5 = CDbl(Replace(Me.TextBox12.Value, "$", ""))

    If IsNumeric(Replace(Me.TextBox13.Value, "$", "")) Then
        val6 = CDbl(Replace(Me.TextBox13.Value, "$", ""))
    Else
        val6 = 0
    End If

    If InStr(txt, "- " & CreateArabicText("daily_post") & ": 30 / 30") > 0 Then
        totalDaily = val5
    End If

    If Not (selectedRank = "Beginning" Or selectedRank = "Growth") Then
        If InStr(txt, "- " & CreateArabicText("weekly_analysis") & ": 1 / 4") > 0 Then totalWeekly = totalWeekly + val6
        If InStr(txt, "- " & CreateArabicText("weekly_analysis") & ": 2 / 4") > 0 Then totalWeekly = totalWeekly + (2 * val6)
        If InStr(txt, "- " & CreateArabicText("weekly_analysis") & ": 3 / 4") > 0 Then totalWeekly = totalWeekly + (3 * val6)
        If InStr(txt, "- " & CreateArabicText("weekly_analysis") & ": 4 / 4") > 0 Then totalWeekly = totalWeekly + (4 * val6)
    End If

    totalValue = totalDaily + totalWeekly
    formattedDaily = Format(totalDaily, "$#,##0.00")
    formattedWeekly = Format(totalWeekly, "$#,##0.00")
    formattedTotal = Format(totalValue, "$#,##0.00")

    Me.TextBox8.Value = formattedTotal
    Me.TextBox9.Value = formattedTotal

    val = Replace(Me.TextBox11.Value, " Lot", "")
    If Trim(val) <> "" And IsNumeric(val) Then
        inputValue = CDbl(val)
        Select Case Me.ComboBox1.Value
            Case "Pro"
                multiplier = Int(inputValue / 200)
                resultValue = multiplier * 100
            Case "Elite"
                multiplier = Int(inputValue / 300)
                resultValue = multiplier * 150
            Case "Growth"
                multiplier = Int(inputValue / 100)
                resultValue = multiplier * 50
        End Select
        Me.TextBox18.Value = Format(resultValue, "$#,##0.00")
    Else
        Me.TextBox18.Value = "$0.00"
    End If

    ' تحويل النص العربي إلى ChrW
    If (Trim(Me.TextBox9.Value) = "" Or Me.TextBox9.Value = "$0.00") And _
       (Trim(Me.TextBox18.Value) = "" Or Me.TextBox18.Value = "$0.00") Then
        ShowArabicMessage ChrW(&H644) & ChrW(&H645) & " " & ChrW(&H64A) & ChrW(&H62D) & ChrW(&H642) & ChrW(&H642) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H64A) & ChrW(&H644) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H634) & ChrW(&H631) & ChrW(&H637) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H637) & ChrW(&H644) & ChrW(&H648) & ChrW(&H628) & " " & ChrW(&H644) & ChrW(&H644) & ChrW(&H62D) & ChrW(&H635) & ChrW(&H648) & ChrW(&H644) & " " & ChrW(&H639) & ChrW(&H644) & ChrW(&H649) & " " & ChrW(&H623) & ChrW(&H64A) & " " & ChrW(&H645) & ChrW(&H643) & ChrW(&H627) & ChrW(&H641) & ChrW(&H623) & ChrW(&H629) & ".", ChrW(&H62A) & ChrW(&H646) & ChrW(&H628) & ChrW(&H64A) & ChrW(&H647), 48
        Exit Sub
    End If

    fullText = Me.TextBox7.Value
    Lines = Split(fullText, vbCrLf)
    cleanedText = ""

    For Each Line In Lines
        If InStr(Line, ChrW(&H627) & ChrW(&H644) & ChrW(&H623) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H646) & ChrW(&H627) & ChrW(&H642) & ChrW(&H635) & ChrW(&H629) & ":") > 0 Then
        Else
            If InStr(Line, ChrW(&H2192)) > 0 Then
                Line = Trim(Left(Line, InStr(Line, ChrW(&H2192)) - 1))
            End If
            If Trim(Line) <> "" Then
                cleanedText = cleanedText & Line & vbCrLf
            End If
        End If
    Next Line

    bonusCount = 0
    numberedLines = ""

    If formattedDaily <> "$0.00" And Trim(formattedDaily) <> "" Then
        bonusCount = bonusCount + 1
        numberedLines = numberedLines & bonusCount & "- " & ChrW(&H645) & ChrW(&H643) & ChrW(&H627) & ChrW(&H641) & ChrW(&H623) & ChrW(&H629) & " " & ChrW(&H639) & ChrW(&H631) & ChrW(&H636) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H646) & ChrW(&H634) & ChrW(&H631) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H64A) & ChrW(&H648) & ChrW(&H645) & ChrW(&H64A) & ": " & formattedDaily & vbCrLf
    End If

    If formattedWeekly <> "$0.00" And Trim(formattedWeekly) <> "" Then
        bonusCount = bonusCount + 1
        numberedLines = numberedLines & bonusCount & "- " & ChrW(&H645) & ChrW(&H643) & ChrW(&H627) & ChrW(&H641) & ChrW(&H623) & ChrW(&H629) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62A) & ChrW(&H62D) & ChrW(&H644) & ChrW(&H64A) & ChrW(&H644) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H623) & ChrW(&H633) & ChrW(&H628) & ChrW(&H648) & ChrW(&H639) & ChrW(&H64A) & ChrW(&H629) & ": " & formattedWeekly & vbCrLf
    End If

    If Me.TextBox18.Value <> "$0.00" And Trim(Me.TextBox18.Value) <> "" Then
        bonusCount = bonusCount + 1
        numberedLines = numberedLines & bonusCount & "- " & ChrW(&H645) & ChrW(&H643) & ChrW(&H627) & ChrW(&H641) & ChrW(&H623) & ChrW(&H629) & " " & ChrW(&H623) & ChrW(&H62D) & ChrW(&H62C) & ChrW(&H627) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62A) & ChrW(&H62F) & ChrW(&H627) & ChrW(&H648) & ChrW(&H644) & ": " & Me.TextBox18.Value & vbCrLf
    End If

    totalAll = totalDaily + totalWeekly
    If Me.TextBox18.Value <> "$0.00" And Trim(Me.TextBox18.Value) <> "" Then
        totalAll = totalAll + CDbl(Replace(Me.TextBox18.Value, "$", ""))
    End If

    If bonusCount > 1 Then
        totalBonusLine = vbCrLf & ChrW(&H627) & ChrW(&H644) & ChrW(&H625) & ChrW(&H62C) & ChrW(&H645) & ChrW(&H627) & ChrW(&H644) & ChrW(&H64A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H643) & ChrW(&H644) & ChrW(&H64A) & " " & ChrW(&H644) & ChrW(&H644) & ChrW(&H645) & ChrW(&H643) & ChrW(&H627) & ChrW(&H641) & ChrW(&H622) & ChrW(&H62A) & ": " & Format(totalAll, "$#,##0.00") & vbCrLf
    Else
        totalBonusLine = ""
    End If

    If IsNumeric(Replace(Me.TextBox11.Value, " Lot", "")) Then
        formattedVolume = Format(Round(CDbl(Replace(Me.TextBox11.Value, " Lot", "")), 2), "#,##0.00") & " Lot"
    Else
        formattedVolume = Me.TextBox11.Value
    End If

    eliteLevelText = ""

    If LCase(Trim(Me.ComboBox1.Value)) = "elite" And Trim(Me.ComboBox3.Value) <> "" Then
        Select Case LCase(Trim(Me.ComboBox3.Value))
            Case "level 1", ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 1"
                eliteLevelText = ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & ": " & ChrW(&H627) & ChrW(&H644) & ChrW(&H623) & ChrW(&H648) & ChrW(&H644)
            Case "level 2", ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 2"
                eliteLevelText = ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & ": " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62B) & ChrW(&H627) & ChrW(&H646) & ChrW(&H64A)
            Case "level 3", ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 3"
                eliteLevelText = ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & ": " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62B) & ChrW(&H627) & ChrW(&H644) & ChrW(&H62B)
            Case Else
                eliteLevelText = ""
        End Select
    End If

    reportText = _
        ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H64A) & ChrW(&H644) & ":" & vbCrLf & _
        ChrW(&H646) & ChrW(&H648) & ChrW(&H639) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & ": " & Me.ComboBox2.Value & vbCrLf & _
        ChrW(&H645) & ChrW(&H631) & ChrW(&H62A) & ChrW(&H628) & ChrW(&H629) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H64A) & ChrW(&H644) & ": " & Me.ComboBox1.Value & vbCrLf & _
        IIf(eliteLevelText <> "", eliteLevelText & vbCrLf, "") & _
        ChrW(&H631) & ChrW(&H642) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & ": " & agencyNumber & vbCrLf & _
        ChrW(&H62D) & ChrW(&H62C) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62A) & ChrW(&H62F) & ChrW(&H627) & ChrW(&H648) & ChrW(&H644) & " " & ChrW(&H62A) & ChrW(&H62D) & ChrW(&H62A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & " " & ChrW(&H62E) & ChrW(&H644) & ChrW(&H627) & ChrW(&H644) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H634) & ChrW(&H647) & ChrW(&H631) & ": " & formattedVolume & vbCrLf & vbCrLf & _
        numberedLines & _
        totalBonusLine & vbCrLf & _
        cleanedText & vbCrLf & _
        ChrW(&H625) & ChrW(&H630) & ChrW(&H627) & " " & ChrW(&H623) & ChrW(&H645) & ChrW(&H643) & ChrW(&H646) & " " & ChrW(&H625) & ChrW(&H636) & ChrW(&H627) & ChrW(&H641) & ChrW(&H629) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62C) & ChrW(&H627) & ChrW(&H626) & ChrW(&H632) & ChrW(&H629) & " " & ChrW(&H644) & ChrW(&H637) & ChrW(&H641) & ChrW(&H627) & vbCrLf & "@AhmedMousa27"

    With New MSForms.DataObject
        .SetText reportText
        .PutInClipboard
    End With

    ShowArabicMessage ChrW(&H62A) & ChrW(&H645) & " " & ChrW(&H646) & ChrW(&H633) & ChrW(&H62E) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62A) & ChrW(&H642) & ChrW(&H631) & ChrW(&H64A) & ChrW(&H631) & " " & ChrW(&H628) & ChrW(&H646) & ChrW(&H62C) & ChrW(&H627) & ChrW(&H62D) & ".", ChrW(&H62A) & ChrW(&H645) & " " & ChrW(&H628) & ChrW(&H646) & ChrW(&H62C) & ChrW(&H627) & ChrW(&H62D), 64

    On Error GoTo 0
End Sub
