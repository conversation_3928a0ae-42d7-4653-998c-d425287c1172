    ' جزء إضافي من الكود - تحويل جميع النصوص العربية إلى ChrW
    
    If (formattedTotal = "$0.00" Or Trim(formattedTotal) = "") And _
       (Trim(Me.TextBox18.Value) = "" Or Me.TextBox18.Value = "$0.00") Then
        ShowArabicMessage ChrW(&H64A) & ChrW(&H648) & ChrW(&H62C) & ChrW(&H62F) & " " & ChrW(&H646) & ChrW(&H642) & ChrW(&H635) & " " & ChrW(&H641) & ChrW(&H64A) & " " & ChrW(&H639) & ChrW(&H62F) & ChrW(&H62F) & " " & ChrW(&H627) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H646) & ChrW(&H634) & ChrW(&H631), ChrW(&H62A) & ChrW(&H646) & ChrW(&H628) & ChrW(&H64A) & ChrW(&H647), 48
        Exit Sub
    End If

    fullText = Me.TextBox7.Value
    Lines = Split(fullText, vbCrLf)
    cleanedText = ""

    For Each Line In Lines
        If InStr(Line, ChrW(&H627) & ChrW(&H644) & ChrW(&H623) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H646) & ChrW(&H627) & ChrW(&H642) & ChrW(&H635) & ChrW(&H629) & ":") > 0 Then
            ' تجاهله
        Else
            If InStr(Line, ChrW(&H2192)) > 0 Then
                Line = Trim(Left(Line, InStr(Line, ChrW(&H2192)) - 1))
            End If
            If Trim(Line) <> "" Then
                cleanedText = cleanedText & Line & vbCrLf
            End If
        End If
    Next Line

    bonusCount = 0
    numberedLines = ""

    If formattedDaily <> "$0.00" And Trim(formattedDaily) <> "" Then
        bonusCount = bonusCount + 1
        numberedLines = numberedLines & bonusCount & "- " & ChrW(&H645) & ChrW(&H643) & ChrW(&H627) & ChrW(&H641) & ChrW(&H623) & ChrW(&H629) & " " & ChrW(&H639) & ChrW(&H631) & ChrW(&H636) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H646) & ChrW(&H634) & ChrW(&H631) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H64A) & ChrW(&H648) & ChrW(&H645) & ChrW(&H64A) & ": " & formattedDaily & vbCrLf
    End If

    If formattedWeekly <> "$0.00" And Trim(formattedWeekly) <> "" Then
        bonusCount = bonusCount + 1
        numberedLines = numberedLines & bonusCount & "- " & ChrW(&H645) & ChrW(&H643) & ChrW(&H627) & ChrW(&H641) & ChrW(&H623) & ChrW(&H629) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62A) & ChrW(&H62D) & ChrW(&H644) & ChrW(&H64A) & ChrW(&H644) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H623) & ChrW(&H633) & ChrW(&H628) & ChrW(&H648) & ChrW(&H639) & ChrW(&H64A) & ChrW(&H629) & ": " & formattedWeekly & vbCrLf
    End If

    If Me.TextBox18.Value <> "$0.00" And Trim(Me.TextBox18.Value) <> "" Then
        bonusCount = bonusCount + 1
        numberedLines = numberedLines & bonusCount & "- " & ChrW(&H645) & ChrW(&H643) & ChrW(&H627) & ChrW(&H641) & ChrW(&H623) & ChrW(&H629) & " " & ChrW(&H623) & ChrW(&H62D) & ChrW(&H62C) & ChrW(&H627) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62A) & ChrW(&H62F) & ChrW(&H627) & ChrW(&H648) & ChrW(&H644) & ": " & Me.TextBox18.Value & vbCrLf
    End If

    totalAll = totalDaily + totalWeekly
    If Me.TextBox18.Value <> "$0.00" And Trim(Me.TextBox18.Value) <> "" Then
        totalAll = totalAll + CDbl(Replace(Me.TextBox18.Value, "$", ""))
    End If

    If bonusCount > 1 Then
        totalBonusLine = vbCrLf & ChrW(&H627) & ChrW(&H644) & ChrW(&H625) & ChrW(&H62C) & ChrW(&H645) & ChrW(&H627) & ChrW(&H644) & ChrW(&H64A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H643) & ChrW(&H644) & ChrW(&H64A) & " " & ChrW(&H644) & ChrW(&H644) & ChrW(&H645) & ChrW(&H643) & ChrW(&H627) & ChrW(&H641) & ChrW(&H622) & ChrW(&H62A) & ": " & Format(totalAll, "$#,##0.00") & vbCrLf
    Else
        totalBonusLine = ""
    End If

    If IsNumeric(Replace(Me.TextBox11.Value, " Lot", "")) Then
        formattedVolume = Format(Round(CDbl(Replace(Me.TextBox11.Value, " Lot", "")), 2), "#,##0.00") & " Lot"
    Else
        formattedVolume = Me.TextBox11.Value
    End If
  
    eliteLevelText = ""

    If selectedRank = "Elite" And Trim(Me.ComboBox3.Value) <> "" Then
        Select Case Me.ComboBox3.Value
            Case ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 1", "Level 1"
                eliteLevelText = ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & ": " & ChrW(&H627) & ChrW(&H644) & ChrW(&H623) & ChrW(&H648) & ChrW(&H644)
            Case ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 2", "Level 2"
                eliteLevelText = ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & ": " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62B) & ChrW(&H627) & ChrW(&H646) & ChrW(&H64A)
            Case ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 3", "Level 3"
                eliteLevelText = ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & ": " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62B) & ChrW(&H627) & ChrW(&H644) & ChrW(&H62B)
            Case ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 4", "Level 4"
                eliteLevelText = ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & ": " & ChrW(&H627) & ChrW(&H644) & ChrW(&H631) & ChrW(&H627) & ChrW(&H628) & ChrW(&H639)
            Case ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 5", "Level 5"
                eliteLevelText = ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & ": " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62E) & ChrW(&H627) & ChrW(&H645) & ChrW(&H633)
            Case Else
                eliteLevelText = ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & ": " & ChrW(&H63A) & ChrW(&H64A) & ChrW(&H631) & " " & ChrW(&H645) & ChrW(&H62D) & ChrW(&H62F) & ChrW(&H62F)
        End Select
    End If
    
    reportText = _
        ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H64A) & ChrW(&H644) & ":" & vbCrLf & _
        ChrW(&H646) & ChrW(&H648) & ChrW(&H639) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & ": " & Me.ComboBox2.Value & vbCrLf & _
        ChrW(&H645) & ChrW(&H631) & ChrW(&H62A) & ChrW(&H628) & ChrW(&H629) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H64A) & ChrW(&H644) & ": " & Me.ComboBox1.Value & vbCrLf & _
        IIf(eliteLevelText <> "", eliteLevelText & vbCrLf, "") & _
        ChrW(&H631) & ChrW(&H642) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & ": " & agencyNumber & vbCrLf & _
        ChrW(&H62D) & ChrW(&H62C) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62A) & ChrW(&H62F) & ChrW(&H627) & ChrW(&H648) & ChrW(&H644) & " " & ChrW(&H62A) & ChrW(&H62D) & ChrW(&H62A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & ": " & formattedVolume & vbCrLf & vbCrLf & _
        numberedLines & _
        totalBonusLine & vbCrLf & _
        cleanedText & vbCrLf & _
        ChrW(&H625) & ChrW(&H630) & ChrW(&H627) & " " & ChrW(&H623) & ChrW(&H645) & ChrW(&H643) & ChrW(&H646) & " " & ChrW(&H625) & ChrW(&H636) & ChrW(&H627) & ChrW(&H641) & ChrW(&H629) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62C) & ChrW(&H627) & ChrW(&H626) & ChrW(&H632) & ChrW(&H629) & " " & ChrW(&H644) & ChrW(&H637) & ChrW(&H641) & ChrW(&H627) & " @AhmedMousa27"
