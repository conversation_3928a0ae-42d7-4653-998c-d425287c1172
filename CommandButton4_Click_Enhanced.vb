Private Sub CommandButton4_Click()
    Dim filePath As String
    Dim wbImport As Workbook
    Dim wsImport As Worksheet
    Dim wsTarget As Worksheet
    Dim importTitle As String
    Dim confirmMsg As String
    Dim confirmTitle As String
    Dim successMsg As String
    Dim successTitle As String
    
    On Error GoTo ErrorHandler
    
    ' تحديد النصوص العربية مع fallback للإنجليزية
    importTitle = CreateArabicText("import_file_title")
    confirmMsg = CreateArabicText("confirm_import_msg")
    confirmTitle = CreateArabicText("confirm_title")
    successMsg = CreateArabicText("import_success_msg")
    successTitle = CreateArabicText("import_success_title")
    
    Set wsTarget = ThisWorkbook.Sheets("Sheet3")

    filePath = Application.GetOpenFilename("Excel / CSV Files (*.xls;*.xlsx;*.xlsm;*.csv), *.xls;*.xlsx;*.xlsm;*.csv", , importTitle)

    If filePath = "False" Then Exit Sub

    If MsgBox(confirmMsg, vbYesNo + vbQuestion, confirmTitle) = vbNo Then Exit Sub

    Application.ScreenUpdating = False
    Application.DisplayAlerts = False

    wsTarget.Cells.Clear

    Set wbImport = Workbooks.Open(filePath)
    Set wsImport = wbImport.Sheets(1)
    wsImport.UsedRange.Copy Destination:=wsTarget.Range("A1")

    wbImport.Close SaveChanges:=False

    Application.DisplayAlerts = True
    Application.ScreenUpdating = True

    MsgBox successMsg, vbInformation, successTitle
    
    Exit Sub
    
ErrorHandler:
    Application.DisplayAlerts = True
    Application.ScreenUpdating = True
    If Not wbImport Is Nothing Then
        wbImport.Close SaveChanges:=False
    End If
    MsgBox "Error occurred during import: " & Err.Description, vbCritical, "Import Error"
End Sub

' دالة مساعدة لإنشاء النصوص العربية مع fallback
Private Function CreateArabicText(textKey As String) As String
    On Error Resume Next
    
    Select Case textKey
        Case "import_file_title"
            CreateArabicText = ChrW(&H64A) & ChrW(&H631) & ChrW(&H62C) & ChrW(&H649) & " " & _
                              ChrW(&H627) & ChrW(&H62E) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H631) & " " & _
                              ChrW(&H645) & ChrW(&H644) & ChrW(&H641) & " Excel " & ChrW(&H623) & ChrW(&H648) & " CSV " & _
                              ChrW(&H644) & ChrW(&H644) & ChrW(&H627) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62F)
            If Err.Number <> 0 Then CreateArabicText = "Please select Excel or CSV file to import"
            
        Case "confirm_import_msg"
            CreateArabicText = ChrW(&H647) & ChrW(&H644) & " " & ChrW(&H623) & ChrW(&H646) & ChrW(&H62A) & " " & _
                              ChrW(&H645) & ChrW(&H62A) & ChrW(&H623) & ChrW(&H643) & ChrW(&H62F) & " " & ChrW(&H623) & ChrW(&H646) & ChrW(&H643) & " " & _
                              ChrW(&H62A) & ChrW(&H631) & ChrW(&H64A) & ChrW(&H62F) & " " & ChrW(&H62D) & ChrW(&H630) & ChrW(&H641) & " " & _
                              ChrW(&H62C) & ChrW(&H645) & ChrW(&H64A) & ChrW(&H639) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H628) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H646) & ChrW(&H627) & ChrW(&H62A) & " " & _
                              ChrW(&H645) & ChrW(&H646) & " Sheet3 " & ChrW(&H648) & ChrW(&H627) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62F) & " " & _
                              ChrW(&H627) & ChrW(&H644) & ChrW(&H628) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H646) & ChrW(&H627) & ChrW(&H62A) & " " & _
                              ChrW(&H627) & ChrW(&H644) & ChrW(&H62C) & ChrW(&H62F) & ChrW(&H64A) & ChrW(&H62F) & ChrW(&H629) & ChrW(&H61F)
            If Err.Number <> 0 Then CreateArabicText = "Are you sure you want to delete all data from Sheet3 and import new data?"
            
        Case "confirm_title"
            CreateArabicText = ChrW(&H62A) & ChrW(&H623) & ChrW(&H643) & ChrW(&H64A) & ChrW(&H62F) & " " & _
                              ChrW(&H627) & ChrW(&H644) & ChrW(&H639) & ChrW(&H645) & ChrW(&H644) & ChrW(&H64A) & ChrW(&H629)
            If Err.Number <> 0 Then CreateArabicText = "Confirm Operation"
            
        Case "import_success_msg"
            CreateArabicText = ChrW(&H62A) & ChrW(&H645) & " " & ChrW(&H62D) & ChrW(&H630) & ChrW(&H641) & " " & _
                              ChrW(&H627) & ChrW(&H644) & ChrW(&H628) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H646) & ChrW(&H627) & ChrW(&H62A) & " " & _
                              ChrW(&H627) & ChrW(&H644) & ChrW(&H642) & ChrW(&H62F) & ChrW(&H64A) & ChrW(&H645) & ChrW(&H629) & " " & _
                              ChrW(&H648) & ChrW(&H627) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62F) & " " & _
                              ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H644) & ChrW(&H641) & " " & ChrW(&H628) & ChrW(&H646) & ChrW(&H62C) & ChrW(&H627) & ChrW(&H62D) & " " & _
                              ChrW(&H625) & ChrW(&H644) & ChrW(&H649) & " Sheet3"
            If Err.Number <> 0 Then CreateArabicText = "Old data deleted and file imported successfully to Sheet3"
            
        Case "import_success_title"
            CreateArabicText = ChrW(&H639) & ChrW(&H645) & ChrW(&H644) & ChrW(&H64A) & ChrW(&H629) & " " & _
                              ChrW(&H627) & ChrW(&H644) & ChrW(&H627) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62F)
            If Err.Number <> 0 Then CreateArabicText = "Import Operation"
            
        Case Else
            CreateArabicText = "Text not found"
    End Select
    
    On Error GoTo 0
End Function
