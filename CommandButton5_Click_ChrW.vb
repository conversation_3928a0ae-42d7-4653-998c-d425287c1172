Private Sub CommandButton5_Click()
    Dim ws As Worksheet
    Dim answer As VbMsgBoxResult
    Dim confirmMsg As String
    Dim confirmTitle As String
    Dim successMsg As String
    Dim successTitle As String
    Dim cancelMsg As String
    Dim cancelTitle As String
    
    ' تحديد ورقة العمل
    Set ws = ThisWorkbook.Sheets("Sheet3")
    
    ' إنشاء النصوص العربية بصيغة ChrW
    confirmMsg = ChrW(&H647) & ChrW(&H644) & " " & ChrW(&H623) & ChrW(&H646) & ChrW(&H62A) & " " & ChrW(&H645) & ChrW(&H62A) & ChrW(&H623) & ChrW(&H643) & ChrW(&H62F) & " " & ChrW(&H623) & ChrW(&H646) & ChrW(&H643) & " " & ChrW(&H62A) & ChrW(&H631) & ChrW(&H64A) & ChrW(&H62F) & " " & ChrW(&H62D) & ChrW(&H630) & ChrW(&H641) & " " & ChrW(&H62C) & ChrW(&H645) & ChrW(&H64A) & ChrW(&H639) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H628) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H646) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H645) & ChrW(&H646) & " Sheet3" & ChrW(&H61F)
    confirmTitle = ChrW(&H62A) & ChrW(&H623) & ChrW(&H643) & ChrW(&H64A) & ChrW(&H62F) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H639) & ChrW(&H645) & ChrW(&H644) & ChrW(&H64A) & ChrW(&H629)
    successMsg = ChrW(&H62A) & ChrW(&H645) & " " & ChrW(&H62D) & ChrW(&H630) & ChrW(&H641) & " " & ChrW(&H62C) & ChrW(&H645) & ChrW(&H64A) & ChrW(&H639) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H628) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H646) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H628) & ChrW(&H646) & ChrW(&H62C) & ChrW(&H627) & ChrW(&H62D) & "."
    successTitle = ChrW(&H62A) & ChrW(&H645) & " " & ChrW(&H628) & ChrW(&H646) & ChrW(&H62C) & ChrW(&H627) & ChrW(&H62D)
    cancelMsg = ChrW(&H62A) & ChrW(&H645) & " " & ChrW(&H625) & ChrW(&H644) & ChrW(&H63A) & ChrW(&H627) & ChrW(&H621) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H639) & ChrW(&H645) & ChrW(&H644) & ChrW(&H64A) & ChrW(&H629) & "."
    cancelTitle = ChrW(&H625) & ChrW(&H644) & ChrW(&H63A) & ChrW(&H627) & ChrW(&H621)
    
    ' رسالة التأكيد
    answer = MsgBox(confirmMsg, vbYesNo + vbQuestion, confirmTitle)
    
    If answer = vbYes Then
        ws.Cells.Clear
        MsgBox successMsg, vbInformation, successTitle
    Else
        MsgBox cancelMsg, vbInformation, cancelTitle
    End If
End Sub
