Private Sub TextBox7_AfterUpdate()
    Dim regex As Object
    Dim matches As Object
    Dim agencyNumber As String
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim cell As Range
    Dim foundMatch As Boolean

    On Error GoTo CleanExit

    ' تأكد أن TextBox7 يحتوي على نص
    If Trim(Me.TextBox7.Value) = "" Then
        Me.ComboBox6.Value = ""
        Exit Sub
    End If

    ' إنشاء كائن التعبير المنتظم لاستخراج رقم الوكالة
    Set regex = CreateObject("VBScript.RegExp")
    If regex Is Nothing Then
        MsgBox ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H643) & ChrW(&H648) & ChrW(&H646) & " VBScript.RegExp " & ChrW(&H63A) & ChrW(&H64A) & ChrW(&H631) & " " & ChrW(&H645) & ChrW(&H62A) & ChrW(&H627) & ChrW(&H62D) & ".", vbCritical, ChrW(&H62E) & ChrW(&H637) & ChrW(&H623) & " " & ChrW(&H641) & ChrW(&H64A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H646) & ChrW(&H638) & ChrW(&H627) & ChrW(&H645)
        Exit Sub
    End If

    With regex
        .Pattern = ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & "\s*(\d+)" ' يبحث عن كلمة "وكالة" ثم رقم
        .IgnoreCase = True
        .Global = False
    End With

    ' استخراج رقم الوكالة من التقرير داخل TextBox7
    If regex.Test(Me.TextBox7.Value) Then
        Set matches = regex.Execute(Me.TextBox7.Value)
        If matches.Count > 0 Then
            agencyNumber = Trim(matches(0).SubMatches(0))
        End If
    Else
        MsgBox "⚠️ " & ChrW(&H644) & ChrW(&H645) & " " & ChrW(&H64A) & ChrW(&H62A) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H639) & ChrW(&H62B) & ChrW(&H648) & ChrW(&H631) & " " & ChrW(&H639) & ChrW(&H644) & ChrW(&H649) & " " & ChrW(&H631) & ChrW(&H642) & ChrW(&H645) & " " & ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & " " & ChrW(&H641) & ChrW(&H64A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62A) & ChrW(&H642) & ChrW(&H631) & ChrW(&H64A) & ChrW(&H631) & ".", vbExclamation, ChrW(&H62A) & ChrW(&H62D) & ChrW(&H630) & ChrW(&H64A) & ChrW(&H631)
        Me.ComboBox6.Value = ""
        Exit Sub
    End If

    ' التأكد من وجود ورقة Sheet3
    On Error Resume Next
    Set ws = ThisWorkbook.Sheets("Sheet3")
    On Error GoTo CleanExit
    If ws Is Nothing Then
        MsgBox "⚠️ " & ChrW(&H644) & ChrW(&H645) & " " & ChrW(&H64A) & ChrW(&H62A) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H639) & ChrW(&H62B) & ChrW(&H648) & ChrW(&H631) & " " & ChrW(&H639) & ChrW(&H644) & ChrW(&H649) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H631) & ChrW(&H642) & ChrW(&H629) & " Sheet3 " & ChrW(&H641) & ChrW(&H64A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H635) & ChrW(&H646) & ChrW(&H641) & ".", vbCritical, ChrW(&H62E) & ChrW(&H637) & ChrW(&H623)
        Exit Sub
    End If

    ' البحث عن رقم الوكالة في العمود A
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    foundMatch = False

    For Each cell In ws.Range("A1:A" & lastRow)
        If Trim(CStr(cell.Value)) = agencyNumber Then
            foundMatch = True
            Exit For
        End If
    Next cell

    ' النتيجة
    If foundMatch Then
        Me.ComboBox6.Value = agencyNumber
    Else
        Me.ComboBox6.Value = ""
        MsgBox "⚠️ " & ChrW(&H631) & ChrW(&H642) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & " " & agencyNumber & " " & ChrW(&H63A) & ChrW(&H64A) & ChrW(&H631) & " " & ChrW(&H645) & ChrW(&H648) & ChrW(&H62C) & ChrW(&H648) & ChrW(&H62F) & " " & ChrW(&H641) & ChrW(&H64A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H628) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H646) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H62F) & ChrW(&H627) & ChrW(&H62E) & ChrW(&H644) & " Sheet3.", vbExclamation, ChrW(&H62A) & ChrW(&H62D) & ChrW(&H630) & ChrW(&H64A) & ChrW(&H631)
    End If

CleanExit:
    On Error Resume Next
    Set regex = Nothing
    Set matches = Nothing
    Set ws = Nothing
End Sub
