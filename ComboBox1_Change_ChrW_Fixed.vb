Private Sub ComboBox1_Change()
    On Error Resume Next
    Me.TextBox10.Value = ""
    Me.TextBox12.Value = ""
    Me.TextBox13.Value = ""
    Me.TextBox8.Value = ""
    
    Select Case Me.ComboBox1.Value
        Case "Bronze":   Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B2").Value, "$#,##0.00")
        Case "Silver":   Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B3").Value, "$#,##0.00")
        Case "Gold":     Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B4").Value, "$#,##0.00")
        Case "Platinum": Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B5").Value, "$#,##0.00")
        Case "Diamond":  Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B6").Value, "$#,##0.00")
        Case "Sapphire": Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B7").Value, "$#,##0.00")
        Case "Emerald":  Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B8").Value, "$#,##0.00")
        Case "king":     Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B9").Value, "$#,##0.00")
        Case "The Legend": Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B10").Value, "$#,##0.00")
        
        Case "Beginning": Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E2").Value, "$#,##0.00")
        Case "Growth":    Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E3").Value, "$#,##0.00")
        Case "Pro"
            Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E4").Value, "$#,##0.00")
            Me.TextBox13.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E11").Value, "$#,##0.00")
        Case "Elite"
            Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E5").Value, "$#,##0.00")
            Me.TextBox13.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E12").Value, "$#,##0.00")
    End Select
    
    Select Case Me.ComboBox1.Value
        Case "Pro", "Elite"
            Me.TextBox13.Visible = True
            Me.Label11.Visible = True
        Case Else
            Me.TextBox13.Visible = False
            Me.Label11.Visible = False
    End Select
    
    If Me.ComboBox1.Value = "Elite" Then
        Me.Label12.Visible = True
        Me.ComboBox3.Visible = True
        Me.ComboBox3.Clear
        Me.ComboBox3.AddItem ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 1"
        Me.ComboBox3.AddItem ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 2"
        Me.ComboBox3.AddItem ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 3"
    Else
        Me.Label12.Visible = False
        Me.ComboBox3.Visible = False
    End If
    
    On Error GoTo 0
End Sub
