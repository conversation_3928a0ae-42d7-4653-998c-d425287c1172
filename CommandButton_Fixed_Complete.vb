Private Sub CommandButton_Click()
    Dim salary As Double
    Dim totalResult As Double
    Dim reportText As String
    Dim formattedSalary As String
    Dim fullText As String
    Dim lineText As String
    Dim agencyNumber As String
    Dim dateFrom As String
    Dim dateTo As String
    Dim posFind As Long, posEndLine As Long
    Dim arrowPos As Long
    Dim pattern As String
    Dim matches As Object, regEx As Object
    Dim reportAgencyNumber As String
    Dim inputValue As Double
    Dim resultValue As Double
    Dim multiplier As Long
    Dim val As String
    Dim Lines As Variant
    Dim Line As Variant
    Dim cleanedText As String
    Dim foundAgency As String
    Dim inCompanySection As Boolean
    Dim patterns As Variant, p As Variant
    Dim dailyBonus As String
    Dim volumeBonus As String
    Dim numberedBonuses As String
    Dim bonusCount As Integer
    Dim totalFormatted As String
    Dim volumeValue As Double
    Dim volumeFormatted As String

    If Not IsNumeric(Replace(Me.TextBox10.Value, "$", "")) Then
        ShowArabicMessage CreateArabicText("select_rank"), CreateArabicText("warning_title"), 48
        Exit Sub
    End If

    If Trim(Me.TextBox11.Value) = "" Or Not IsNumeric(Replace(Me.TextBox11.Value, " Lot", "")) Then
        ShowArabicMessage CreateArabicText("enter_volume"), CreateArabicText("warning_title"), 48
        Me.TextBox9.Value = ""
        Exit Sub
    End If

    If CDbl(Replace(Me.TextBox11.Value, " Lot", "")) < 100 Then
        ShowArabicMessage CreateArabicText("no_salary"), CreateArabicText("error_title"), 16
        Me.TextBox9.Value = ""
        Exit Sub
    End If

    fullText = Me.TextBox7.Value
    agencyNumber = Trim(Me.ComboBox6.Value)

    '--- تحقق رقم الوكالة ---
    Set regEx = CreateObject("VBScript.RegExp")
    regEx.IgnoreCase = True
    regEx.Global = True
    foundAgency = ""

    ' أنماط مختلفة لاستخراج رقم الوكالة من التقرير
    patterns = Array(ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & "\s*[:\-]?\s*(\d+)", _
                     "Agency\s*[:\-]?\s*(\d+)", _
                     ChrW(&H631) & ChrW(&H642) & ChrW(&H645) & "\s*" & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & "\s*[:\-]?\s*(\d+)", _
                     ChrW(&H648) & ChrW(&H643) & ChrW(&H64A) & ChrW(&H644) & "\s*[:\-]?\s*(\d+)")

    For Each p In patterns
        regEx.pattern = p
        If regEx.Test(fullText) Then
            Set matches = regEx.Execute(fullText)
            If matches.Count > 0 Then
                If matches(0).SubMatches.Count > 0 Then
                    foundAgency = Trim(matches(0).SubMatches(0))
                    Exit For
                End If
            End If
        End If
    Next p

    ' خطة احتياطية إذا لم نجد أي رقم بعد الكلمة "وكالة"
    If foundAgency = "" Then
        regEx.pattern = "(\d{3,})"
        If regEx.Test(fullText) Then
            Set matches = regEx.Execute(fullText)
            If matches.Count > 0 Then
                foundAgency = Trim(matches(0).SubMatches(0))
            End If
        End If
    End If

    ' التحقق النهائي
    If foundAgency = "" Then
        ShowArabicMessage ChrW(&H62A) & ChrW(&H639) & ChrW(&H630) & ChrW(&H631) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H639) & ChrW(&H62B) & ChrW(&H648) & ChrW(&H631) & " " & ChrW(&H639) & ChrW(&H644) & ChrW(&H649) & " " & ChrW(&H631) & ChrW(&H642) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & " " & ChrW(&H62F) & ChrW(&H627) & ChrW(&H62E) & ChrW(&H644) & " " & ChrW(&H646) & ChrW(&H635) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62A) & ChrW(&H642) & ChrW(&H631) & ChrW(&H64A) & ChrW(&H631) & ". " & ChrW(&H64A) & ChrW(&H631) & ChrW(&H62C) & ChrW(&H649) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62A) & ChrW(&H62D) & ChrW(&H642) & ChrW(&H642) & " " & ChrW(&H645) & ChrW(&H646) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62A) & ChrW(&H642) & ChrW(&H631) & ChrW(&H64A) & ChrW(&H631) & " " & ChrW(&H642) & ChrW(&H628) & ChrW(&H644) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H62A) & ChrW(&H627) & ChrW(&H628) & ChrW(&H639) & ChrW(&H629) & ".", _
                          ChrW(&H62E) & ChrW(&H637) & ChrW(&H623), 48
        Exit Sub
    ElseIf foundAgency <> agencyNumber Then
        ShowArabicMessage ChrW(&H631) & ChrW(&H642) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & " " & ChrW(&H641) & ChrW(&H64A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62A) & ChrW(&H642) & ChrW(&H631) & ChrW(&H64A) & ChrW(&H631) & " (" & foundAgency & ") " & ChrW(&H644) & ChrW(&H627) & " " & ChrW(&H64A) & ChrW(&H637) & ChrW(&H627) & ChrW(&H628) & ChrW(&H642) & " " & ChrW(&H631) & ChrW(&H642) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H62D) & ChrW(&H62F) & ChrW(&H62F) & " (" & agencyNumber & "). " & ChrW(&H644) & ChrW(&H627) & " " & ChrW(&H64A) & ChrW(&H645) & ChrW(&H643) & ChrW(&H646) & " " & ChrW(&H625) & ChrW(&H62A) & ChrW(&H645) & ChrW(&H627) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H639) & ChrW(&H645) & ChrW(&H644) & ChrW(&H64A) & ChrW(&H629) & ".", _
                          ChrW(&H62E) & ChrW(&H637) & ChrW(&H623), 48
        Exit Sub
    End If
    '--- نهاية تحقق رقم الوكالة ---

    salary = CDbl(Replace(Me.TextBox10.Value, "$", ""))
    totalResult = 0

    ' عمليات الحساب المعتادة مع إضافة النص المفقود
    If InStr(fullText, ChrW(&H639) & ChrW(&H631) & ChrW(&H636) & " " & ChrW(&H646) & ChrW(&H634) & ChrW(&H631) & " " & ChrW(&H64A) & ChrW(&H648) & ChrW(&H645) & ChrW(&H64A) & ": 30 / 30") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(fullText, ChrW(&H639) & ChrW(&H631) & ChrW(&H636) & " " & ChrW(&H628) & ChrW(&H648) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H625) & ChrW(&H62B) & ChrW(&H628) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H633) & ChrW(&H62D) & ChrW(&H628) & ": 4 / 4") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(fullText, ChrW(&H645) & ChrW(&H646) & ChrW(&H634) & ChrW(&H648) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H623) & ChrW(&H633) & ChrW(&H628) & ChrW(&H627) & ChrW(&H628) & " " & ChrW(&H627) & ChrW(&H62E) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H631) & " " & ChrW(&H625) & ChrW(&H646) & ChrW(&H632) & ChrW(&H648) & ": 2 / 2") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(fullText, ChrW(&H646) & ChrW(&H62F) & ChrW(&H648) & ChrW(&H629) & " " & ChrW(&H644) & ChrW(&H627) & ChrW(&H64A) & ChrW(&H641) & ": 2 / 2") > 0 Then totalResult = totalResult + (salary * 0.1)
    If InStr(fullText, ChrW(&H639) & ChrW(&H631) & ChrW(&H636) & " " & ChrW(&H631) & ChrW(&H64A) & ChrW(&H644) & ChrW(&H632) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H62A) & ChrW(&H641) & ChrW(&H627) & ChrW(&H639) & ChrW(&H644) & ChrW(&H64A) & ChrW(&H629) & ": 2 / 2") > 0 Then totalResult = totalResult + (salary * 0.08)
    If InStr(fullText, ChrW(&H639) & ChrW(&H631) & ChrW(&H636) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62F) & ChrW(&H648) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62A) & ChrW(&H639) & ChrW(&H644) & ChrW(&H64A) & ChrW(&H645) & ChrW(&H64A) & ChrW(&H629) & ": 5 / 5") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(fullText, ChrW(&H639) & ChrW(&H631) & ChrW(&H636) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62A) & ChrW(&H62D) & ChrW(&H644) & ChrW(&H64A) & ChrW(&H644) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H623) & ChrW(&H633) & ChrW(&H628) & ChrW(&H648) & ChrW(&H639) & ChrW(&H64A) & ChrW(&H629) & ": 4 / 4") > 0 Then totalResult = totalResult + (salary * 0.12)
    ' إضافة النص المفقود: "نشر إثبات سحب - بونص ترحيبي: 2 / 2"
    If InStr(fullText, ChrW(&H646) & ChrW(&H634) & ChrW(&H631) & " " & ChrW(&H625) & ChrW(&H62B) & ChrW(&H628) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H633) & ChrW(&H62D) & ChrW(&H628) & " - " & ChrW(&H628) & ChrW(&H648) & ChrW(&H646) & ChrW(&H635) & " " & ChrW(&H62A) & ChrW(&H631) & ChrW(&H62D) & ChrW(&H64A) & ChrW(&H628) & ChrW(&H64A) & ": 2 / 2") > 0 Then totalResult = totalResult + (salary * 0.12)
    If InStr(fullText, ChrW(&H62A) & ChrW(&H639) & ChrW(&H627) & ChrW(&H648) & ChrW(&H646) & " " & ChrW(&H645) & ChrW(&H639) & " " & ChrW(&H634) & ChrW(&H631) & ChrW(&H643) & ChrW(&H629) & ": 1 / 1") > 0 Then totalResult = totalResult + (salary * 0.1)

    formattedSalary = Format(totalResult, "$#,##0.00")
    Me.TextBox9.Value = formattedSalary
    Me.TextBox8.Value = formattedSalary

    If Trim(Me.ComboBox2.Value) = "" Then
        ShowArabicMessage CreateArabicText("select_agency_type"), CreateArabicText("warning_title"), 48
        Me.ComboBox2.SetFocus
        Exit Sub
    End If
    If Trim(Me.ComboBox1.Value) = "" Then
        ShowArabicMessage CreateArabicText("select_rank"), CreateArabicText("warning_title"), 48
        Me.ComboBox1.SetFocus
        Exit Sub
    End If
    val = Replace(Me.TextBox11.Value, " Lot", "")
    If Trim(val) = "" Or Not IsNumeric(val) Then
        ShowArabicMessage CreateArabicText("enter_correct_number"), CreateArabicText("warning_title"), 48
        Me.TextBox11.SetFocus
        Exit Sub
    End If

    inputValue = CDbl(val)
    Select Case Me.ComboBox1.Value
        Case "Pro"
            multiplier = Int(inputValue / 200)
            resultValue = multiplier * 100
        Case "Elite"
            multiplier = Int(inputValue / 300)
            resultValue = multiplier * 150
        Case "Bronze"
            If inputValue > 200 Then
                resultValue = (inputValue - 200) * 0.25
            Else
                resultValue = 0
            End If
        Case "Silver"
            If inputValue > 300 Then
                resultValue = (inputValue - 300) * 0.25
            Else
                resultValue = 0
            End If
        Case "Gold"
            If inputValue > 600 Then
                resultValue = (inputValue - 600) * 0.25
            Else
                resultValue = 0
            End If
        Case "Platinum"
            If inputValue > 1200 Then
                resultValue = (inputValue - 1200) * 0.25
            Else
                resultValue = 0
            End If
        Case "Diamond"
            If inputValue > 2500 Then
                resultValue = (inputValue - 2500) * 0.25
            Else
                resultValue = 0
            End If
        Case "Sapphire"
            If inputValue > 5000 Then
                resultValue = (inputValue - 5000) * 0.25
            Else
                resultValue = 0
            End If
        Case "Emerald"
            If inputValue > 10000 Then
                resultValue = (inputValue - 10000) * 0.25
            Else
                resultValue = 0
            End If
        Case "King"
            If inputValue > 20000 Then
                resultValue = (inputValue - 20000) * 0.25
            Else
                resultValue = 0
            End If
        Case "The Legend"
            If inputValue > 40000 Then
                resultValue = (inputValue - 40000) * 0.25
            Else
                resultValue = 0
            End If
        Case Else
            ShowArabicMessage CreateArabicText("select_rank"), CreateArabicText("warning_title"), 48
            Exit Sub
    End Select

    Me.TextBox18.Value = Format(resultValue, "$#,##0.00")

    ' التحقق من وجود مكافآت - يستمر إذا كان هناك مكافأة في TextBox18 حتى لو لم يكن هناك راتب
    If (Trim(Me.TextBox9.Value) = "" Or Me.TextBox9.Value = "$0.00") And _
       (Trim(Me.TextBox18.Value) = "" Or Me.TextBox18.Value = "$0.00") Then
        ShowArabicMessage ChrW(&H644) & ChrW(&H645) & " " & ChrW(&H64A) & ChrW(&H62D) & ChrW(&H642) & ChrW(&H642) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H64A) & ChrW(&H644) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H634) & ChrW(&H631) & ChrW(&H637) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H637) & ChrW(&H644) & ChrW(&H648) & ChrW(&H628) & " " & ChrW(&H644) & ChrW(&H644) & ChrW(&H62D) & ChrW(&H635) & ChrW(&H648) & ChrW(&H644) & " " & ChrW(&H639) & ChrW(&H644) & ChrW(&H649) & " " & ChrW(&H623) & ChrW(&H64A) & " " & ChrW(&H645) & ChrW(&H643) & ChrW(&H627) & ChrW(&H641) & ChrW(&H623) & ChrW(&H629) & ".", ChrW(&H62A) & ChrW(&H646) & ChrW(&H628) & ChrW(&H64A) & ChrW(&H647), 48
        Exit Sub
    End If

    Lines = Split(fullText, vbCrLf)
    cleanedText = ""
    For Each Line In Lines
        ' تفعيل علامة عند الوصول الى قسم "تعاون مع شركة" في الأسفل
        If InStr(Line, ChrW(&H62A) & ChrW(&H639) & ChrW(&H627) & ChrW(&H648) & ChrW(&H646) & " " & ChrW(&H645) & ChrW(&H639) & " " & ChrW(&H634) & ChrW(&H631) & ChrW(&H643) & ChrW(&H629)) > 0 Then
            inCompanySection = True
        End If
        ' حذف الأسطر غير المرغوب فيها: "أيام ناقصة" و "نشر يومي:" (بدون كلمة "عرض")
        If InStr(Line, ChrW(&H623) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H645) & " " & ChrW(&H646) & ChrW(&H627) & ChrW(&H642) & ChrW(&H635) & ChrW(&H629)) > 0 Or _
           (inCompanySection And InStr(Line, ChrW(&H646) & ChrW(&H634) & ChrW(&H631) & " " & ChrW(&H64A) & ChrW(&H648) & ChrW(&H645) & ChrW(&H64A) & ":") > 0 And InStr(Line, ChrW(&H639) & ChrW(&H631) & ChrW(&H636)) = 0) Then
            ' تخطي هذا السطر
        Else
            If InStr(Line, ChrW(&H2192)) > 0 Then
                Line = Trim(Left(Line, InStr(Line, ChrW(&H2192)) - 1))
            End If
            If Trim(Line) <> "" Then cleanedText = cleanedText & Line & vbCrLf
        End If
    Next Line

    dailyBonus = ""
    volumeBonus = ""
    numberedBonuses = ""
    bonusCount = 0

    If formattedSalary <> "$0.00" And formattedSalary <> "" Then
        bonusCount = bonusCount + 1
        dailyBonus = bonusCount & "- " & ChrW(&H627) & ChrW(&H644) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62A) & ChrW(&H628) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H62D) & ChrW(&H642) & ": " & formattedSalary & vbCrLf
        numberedBonuses = numberedBonuses & dailyBonus
    End If

    If Me.TextBox18.Value <> "$0.00" And Trim(Me.TextBox18.Value) <> "" Then
        bonusCount = bonusCount + 1
        volumeBonus = bonusCount & "- " & ChrW(&H645) & ChrW(&H643) & ChrW(&H627) & ChrW(&H641) & ChrW(&H623) & ChrW(&H629) & " " & ChrW(&H623) & ChrW(&H62D) & ChrW(&H62C) & ChrW(&H627) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62A) & ChrW(&H62F) & ChrW(&H627) & ChrW(&H648) & ChrW(&H644) & ": " & Me.TextBox18.Value & vbCrLf
        numberedBonuses = numberedBonuses & volumeBonus
    End If

    If bonusCount > 1 Then
        totalFormatted = ChrW(&H627) & ChrW(&H644) & ChrW(&H625) & ChrW(&H62C) & ChrW(&H645) & ChrW(&H627) & ChrW(&H644) & ChrW(&H64A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H643) & ChrW(&H644) & ChrW(&H64A) & " " & ChrW(&H644) & ChrW(&H644) & ChrW(&H645) & ChrW(&H643) & ChrW(&H627) & ChrW(&H641) & ChrW(&H622) & ChrW(&H62A) & ": " & Format(totalResult + resultValue, "$#,##0.00") & vbCrLf & vbCrLf
    Else
        totalFormatted = vbCrLf
    End If
    
    If IsNumeric(Me.TextBox11.Value) Then
        volumeValue = Round(CDbl(Me.TextBox11.Value), 2)
    ElseIf InStr(Me.TextBox11.Value, "Lot") > 0 Then
        volumeValue = Round(CDbl(Replace(Me.TextBox11.Value, "Lot", "")), 2)
    Else
        volumeValue = 0
    End If
    volumeFormatted = ChrW(&H62D) & ChrW(&H62C) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62A) & ChrW(&H62F) & ChrW(&H627) & ChrW(&H648) & ChrW(&H644) & " " & ChrW(&H62A) & ChrW(&H62D) & ChrW(&H62A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & " " & ChrW(&H62E) & ChrW(&H644) & ChrW(&H627) & ChrW(&H644) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H634) & ChrW(&H647) & ChrW(&H631) & ": " & volumeValue & " Lot" & vbCrLf & vbCrLf

    reportText = _
        ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H64A) & ChrW(&H644) & ":" & vbCrLf & _
        ChrW(&H646) & ChrW(&H648) & ChrW(&H639) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & ": " & Me.ComboBox2.Value & vbCrLf & _
        ChrW(&H645) & ChrW(&H631) & ChrW(&H62A) & ChrW(&H628) & ChrW(&H629) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H64A) & ChrW(&H644) & ": " & Me.ComboBox1.Value & vbCrLf & _
        ChrW(&H631) & ChrW(&H642) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & ": " & Me.ComboBox6.Value & vbCrLf & _
        volumeFormatted & _
        numberedBonuses & _
        totalFormatted & _
        cleanedText & vbCrLf & _
        ChrW(&H625) & ChrW(&H630) & ChrW(&H627) & " " & ChrW(&H623) & ChrW(&H645) & ChrW(&H643) & ChrW(&H646) & " " & ChrW(&H625) & ChrW(&H636) & ChrW(&H627) & ChrW(&H641) & ChrW(&H629) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62C) & ChrW(&H627) & ChrW(&H626) & ChrW(&H632) & ChrW(&H629) & " " & ChrW(&H644) & ChrW(&H637) & ChrW(&H641) & ChrW(&H627) & vbCrLf & "@AhmedMousa27"
    
    With New MSForms.DataObject
        .SetText reportText
        .PutInClipboard
    End With

    ShowArabicMessage ChrW(&H62A) & ChrW(&H645) & " " & ChrW(&H646) & ChrW(&H633) & ChrW(&H62E) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62A) & ChrW(&H642) & ChrW(&H631) & ChrW(&H64A) & ChrW(&H631) & " " & ChrW(&H628) & ChrW(&H646) & ChrW(&H62C) & ChrW(&H627) & ChrW(&H62D) & ".", ChrW(&H62A) & ChrW(&H645) & " " & ChrW(&H628) & ChrW(&H646) & ChrW(&H62C) & ChrW(&H627) & ChrW(&H62D), 64

End Sub
