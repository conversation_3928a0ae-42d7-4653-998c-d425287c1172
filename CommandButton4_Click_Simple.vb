Private Sub CommandButton4_Click()
    Dim filePath As String
    Dim wbImport As Workbook
    Dim wsImport As Worksheet
    Dim wsTarget As Worksheet
    Dim importTitle As String
    Dim confirmMsg As String
    Dim confirmTitle As String
    Dim successMsg As String
    Dim successTitle As String

    Set wsTarget = ThisWorkbook.Sheets("Sheet3")

    ' إنشاء النصوص العربية
    importTitle = GetArabicText("يرجى اختيار ملف Excel أو CSV للاستيراد")
    confirmMsg = GetArabicText("هل أنت متأكد أنك تريد حذف جميع البيانات من Sheet3 واستيراد البيانات الجديدة؟")
    confirmTitle = GetArabicText("تأكيد العملية")
    successMsg = GetArabicText("تم حذف البيانات القديمة واستيراد الملف بنجاح إلى Sheet3")
    successTitle = GetArabicText("عملية الاستيراد")

    filePath = Application.GetOpenFilename("Excel / CSV Files (*.xls;*.xlsx;*.xlsm;*.csv), *.xls;*.xlsx;*.xlsm;*.csv", , importTitle)

    If filePath = "False" Then Exit Sub

    If MsgBox(confirmMsg, vbYesNo + vbQuestion, confirmTitle) = vbNo Then Exit Sub

    Application.ScreenUpdating = False
    Application.DisplayAlerts = False

    wsTarget.Cells.Clear

    Set wbImport = Workbooks.Open(filePath)
    Set wsImport = wbImport.Sheets(1)
    wsImport.UsedRange.Copy Destination:=wsTarget.Range("A1")

    wbImport.Close SaveChanges:=False

    Application.DisplayAlerts = True
    Application.ScreenUpdating = True

    MsgBox successMsg, vbInformation, successTitle
End Sub

' دالة لتحويل النص العربي إلى Unicode مع fallback
Private Function GetArabicText(arabicText As String) As String
    On Error Resume Next

    ' محاولة استخدام النص العربي مباشرة أولاً
    GetArabicText = arabicText

    ' إذا فشل، استخدم النص الإنجليزي كبديل
    If Err.Number <> 0 Or GetArabicText = "" Then
        Select Case arabicText
            Case "يرجى اختيار ملف Excel أو CSV للاستيراد"
                GetArabicText = "Please select Excel or CSV file to import"
            Case "هل أنت متأكد أنك تريد حذف جميع البيانات من Sheet3 واستيراد البيانات الجديدة؟"
                GetArabicText = "Are you sure you want to delete all data from Sheet3 and import new data?"
            Case "تأكيد العملية"
                GetArabicText = "Confirm Operation"
            Case "تم حذف البيانات القديمة واستيراد الملف بنجاح إلى Sheet3"
                GetArabicText = "Old data deleted and file imported successfully to Sheet3"
            Case "عملية الاستيراد"
                GetArabicText = "Import Operation"
            Case Else
                GetArabicText = arabicText
        End Select
    End If

    On Error GoTo 0
End Function
