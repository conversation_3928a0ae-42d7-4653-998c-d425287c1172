Private Sub CommandButton4_Click()
    Dim filePath As String
    Dim wbImport As Workbook
    Dim wsImport As Worksheet
    Dim wsTarget As Worksheet
    Dim importTitle As String
    Dim confirmMsg As String
    Dim confirmTitle As String
    Dim successMsg As String
    Dim successTitle As String

    Set wsTarget = ThisWorkbook.Sheets("Sheet3")

    ' إنشاء النصوص العربية بصيغة Unicode
    importTitle = GetArabicText(ChrW(&H64A) & ChrW(&H631) & ChrW(&H62C) & ChrW(&H649) & " " & ChrW(&H627) & ChrW(&H62E) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H631) & " " & ChrW(&H645) & ChrW(&H644) & ChrW(&H641) & " Excel " & ChrW(&H623) & ChrW(&H648) & " CSV " & ChrW(&H644) & ChrW(&H644) & ChrW(&H627) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62F))
    confirmMsg = GetArabicText(ChrW(&H647) & ChrW(&H644) & " " & ChrW(&H623) & ChrW(&H646) & ChrW(&H62A) & " " & ChrW(&H645) & ChrW(&H62A) & ChrW(&H623) & ChrW(&H643) & ChrW(&H62F) & " " & ChrW(&H623) & ChrW(&H646) & ChrW(&H643) & " " & ChrW(&H62A) & ChrW(&H631) & ChrW(&H64A) & ChrW(&H62F) & " " & ChrW(&H62D) & ChrW(&H630) & ChrW(&H641) & " " & ChrW(&H62C) & ChrW(&H645) & ChrW(&H64A) & ChrW(&H639) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H628) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H646) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H645) & ChrW(&H646) & " Sheet3 " & ChrW(&H648) & ChrW(&H627) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62F) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H628) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H646) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62C) & ChrW(&H62F) & ChrW(&H64A) & ChrW(&H62F) & ChrW(&H629) & ChrW(&H61F))
    confirmTitle = GetArabicText(ChrW(&H62A) & ChrW(&H623) & ChrW(&H643) & ChrW(&H64A) & ChrW(&H62F) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H639) & ChrW(&H645) & ChrW(&H644) & ChrW(&H64A) & ChrW(&H629))
    successMsg = GetArabicText(ChrW(&H62A) & ChrW(&H645) & " " & ChrW(&H62D) & ChrW(&H630) & ChrW(&H641) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H628) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H646) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H642) & ChrW(&H62F) & ChrW(&H64A) & ChrW(&H645) & ChrW(&H629) & " " & ChrW(&H648) & ChrW(&H627) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62F) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H644) & ChrW(&H641) & " " & ChrW(&H628) & ChrW(&H646) & ChrW(&H62C) & ChrW(&H627) & ChrW(&H62D) & " " & ChrW(&H625) & ChrW(&H644) & ChrW(&H649) & " Sheet3")
    successTitle = GetArabicText(ChrW(&H639) & ChrW(&H645) & ChrW(&H644) & ChrW(&H64A) & ChrW(&H629) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H627) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62F))

    filePath = Application.GetOpenFilename("Excel / CSV Files (*.xls;*.xlsx;*.xlsm;*.csv), *.xls;*.xlsx;*.xlsm;*.csv", , importTitle)

    If filePath = "False" Then Exit Sub

    If MsgBox(confirmMsg, vbYesNo + vbQuestion, confirmTitle) = vbNo Then Exit Sub

    Application.ScreenUpdating = False
    Application.DisplayAlerts = False

    wsTarget.Cells.Clear

    Set wbImport = Workbooks.Open(filePath)
    Set wsImport = wbImport.Sheets(1)
    wsImport.UsedRange.Copy Destination:=wsTarget.Range("A1")

    wbImport.Close SaveChanges:=False

    Application.DisplayAlerts = True
    Application.ScreenUpdating = True

    MsgBox successMsg, vbInformation, successTitle
End Sub

' دالة لتحويل النص العربي إلى Unicode مع fallback
Private Function GetArabicText(arabicText As String) As String
    On Error Resume Next

    ' محاولة استخدام النص العربي مباشرة أولاً
    GetArabicText = arabicText

    ' إذا فشل، استخدم النص الإنجليزي كبديل
    If Err.Number <> 0 Or GetArabicText = "" Then
        Select Case arabicText
            Case ChrW(&H64A) & ChrW(&H631) & ChrW(&H62C) & ChrW(&H649) & " " & ChrW(&H627) & ChrW(&H62E) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H631) & " " & ChrW(&H645) & ChrW(&H644) & ChrW(&H641) & " Excel " & ChrW(&H623) & ChrW(&H648) & " CSV " & ChrW(&H644) & ChrW(&H644) & ChrW(&H627) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62F)
                GetArabicText = "Please select Excel or CSV file to import"
            Case ChrW(&H647) & ChrW(&H644) & " " & ChrW(&H623) & ChrW(&H646) & ChrW(&H62A) & " " & ChrW(&H645) & ChrW(&H62A) & ChrW(&H623) & ChrW(&H643) & ChrW(&H62F) & " " & ChrW(&H623) & ChrW(&H646) & ChrW(&H643) & " " & ChrW(&H62A) & ChrW(&H631) & ChrW(&H64A) & ChrW(&H62F) & " " & ChrW(&H62D) & ChrW(&H630) & ChrW(&H641) & " " & ChrW(&H62C) & ChrW(&H645) & ChrW(&H64A) & ChrW(&H639) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H628) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H646) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H645) & ChrW(&H646) & " Sheet3 " & ChrW(&H648) & ChrW(&H627) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62F) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H628) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H646) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62C) & ChrW(&H62F) & ChrW(&H64A) & ChrW(&H62F) & ChrW(&H629) & ChrW(&H61F)
                GetArabicText = "Are you sure you want to delete all data from Sheet3 and import new data?"
            Case ChrW(&H62A) & ChrW(&H623) & ChrW(&H643) & ChrW(&H64A) & ChrW(&H62F) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H639) & ChrW(&H645) & ChrW(&H644) & ChrW(&H64A) & ChrW(&H629)
                GetArabicText = "Confirm Operation"
            Case ChrW(&H62A) & ChrW(&H645) & " " & ChrW(&H62D) & ChrW(&H630) & ChrW(&H641) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H628) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H646) & ChrW(&H627) & ChrW(&H62A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H642) & ChrW(&H62F) & ChrW(&H64A) & ChrW(&H645) & ChrW(&H629) & " " & ChrW(&H648) & ChrW(&H627) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62F) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H644) & ChrW(&H641) & " " & ChrW(&H628) & ChrW(&H646) & ChrW(&H62C) & ChrW(&H627) & ChrW(&H62D) & " " & ChrW(&H625) & ChrW(&H644) & ChrW(&H649) & " Sheet3"
                GetArabicText = "Old data deleted and file imported successfully to Sheet3"
            Case ChrW(&H639) & ChrW(&H645) & ChrW(&H644) & ChrW(&H64A) & ChrW(&H629) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H627) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62F)
                GetArabicText = "Import Operation"
            Case Else
                GetArabicText = arabicText
        End Select
    End If

    On Error GoTo 0
End Function
