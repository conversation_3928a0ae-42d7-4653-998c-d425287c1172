Private Sub CommandButton4_Click()
    Dim filePath As String
    Dim wbImport As Workbook
    Dim wsImport As Worksheet
    Dim wsTarget As Worksheet

    Set wsTarget = ThisWorkbook.Sheets("Sheet3")

    ' استخدام النص الإنجليزي كبديل آمن
    filePath = Application.GetOpenFilename("Excel / CSV Files (*.xls;*.xlsx;*.xlsm;*.csv), *.xls;*.xlsx;*.xlsm;*.csv", , "Please select Excel or CSV file to import")

    If filePath = "False" Then Exit Sub

    If MsgBox("Are you sure you want to delete all data from Sheet3 and import new data?", vbYesNo + vbQuestion, "Confirm Operation") = vbNo Then Exit Sub

    Application.ScreenUpdating = False
    Application.DisplayAlerts = False

    wsTarget.Cells.Clear

    Set wbImport = Workbooks.Open(filePath)
    Set wsImport = wbImport.Sheets(1)
    wsImport.UsedRange.Copy Destination:=wsTarget.Range("A1")

    wbImport.Close SaveChanges:=False

    Application.DisplayAlerts = True
    Application.ScreenUpdating = True

    MsgBox "Old data deleted and file imported successfully to Sheet3", vbInformation, "Import Operation"
End Sub
