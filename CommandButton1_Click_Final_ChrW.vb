    ' جزء من CommandButton1_Click - تحويل جميع النصوص العربية إلى ChrW
    
    If (Trim(Me.TextBox9.Value) = "" Or Me.TextBox9.Value = "$0.00") And _
       (Trim(Me.TextBox18.Value) = "" Or Me.TextBox18.Value = "$0.00") Then
        ShowArabicMessage ChrW(&H644) & ChrW(&H645) & " " & ChrW(&H64A) & ChrW(&H62D) & ChrW(&H642) & ChrW(&H642) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H64A) & ChrW(&H644) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H634) & ChrW(&H631) & ChrW(&H637) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H637) & ChrW(&H644) & ChrW(&H648) & ChrW(&H628) & " " & ChrW(&H644) & ChrW(&H644) & ChrW(&H62D) & ChrW(&H635) & ChrW(&H648) & ChrW(&H644) & " " & ChrW(&H639) & ChrW(&H644) & ChrW(&H649) & " " & ChrW(&H623) & ChrW(&H64A) & " " & ChrW(&H645) & ChrW(&H643) & ChrW(&H627) & ChrW(&H641) & ChrW(&H623) & ChrW(&H629) & ".", ChrW(&H62A) & ChrW(&H646) & ChrW(&H628) & ChrW(&H64A) & ChrW(&H647), 48
        Exit Sub
    End If

    Lines = Split(fullText, vbCrLf)
    cleanedText = ""
    For Each Line In Lines
        If InStr(Line, ChrW(&H623) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H645) & " " & ChrW(&H646) & ChrW(&H627) & ChrW(&H642) & ChrW(&H635) & ChrW(&H629)) > 0 Or InStr(Line, ChrW(&H646) & ChrW(&H634) & ChrW(&H631) & " " & ChrW(&H64A) & ChrW(&H648) & ChrW(&H645) & ChrW(&H64A) & ":") > 0 Then
        Else
            If InStr(Line, ChrW(&H2192)) > 0 Then
                Line = Trim(Left(Line, InStr(Line, ChrW(&H2192)) - 1))
            End If
            If Trim(Line) <> "" Then cleanedText = cleanedText & Line & vbCrLf
        End If
    Next Line

    dailyBonus = ""
    volumeBonus = ""
    numberedBonuses = ""
    bonusCount = 0

    If formattedSalary <> "$0.00" And formattedSalary <> "" Then
        bonusCount = bonusCount + 1
        dailyBonus = bonusCount & "- " & ChrW(&H627) & ChrW(&H644) & ChrW(&H631) & ChrW(&H627) & ChrW(&H62A) & ChrW(&H628) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H62D) & ChrW(&H642) & ": " & formattedSalary & vbCrLf
        numberedBonuses = numberedBonuses & dailyBonus
    End If

    If Me.TextBox18.Value <> "$0.00" And Trim(Me.TextBox18.Value) <> "" Then
        bonusCount = bonusCount + 1
        volumeBonus = bonusCount & "- " & ChrW(&H645) & ChrW(&H643) & ChrW(&H627) & ChrW(&H641) & ChrW(&H623) & ChrW(&H629) & " " & ChrW(&H623) & ChrW(&H62D) & ChrW(&H62C) & ChrW(&H627) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62A) & ChrW(&H62F) & ChrW(&H627) & ChrW(&H648) & ChrW(&H644) & ": " & Me.TextBox18.Value & vbCrLf
        numberedBonuses = numberedBonuses & volumeBonus
    End If

    If bonusCount > 1 Then
        totalFormatted = ChrW(&H627) & ChrW(&H644) & ChrW(&H625) & ChrW(&H62C) & ChrW(&H645) & ChrW(&H627) & ChrW(&H644) & ChrW(&H64A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H643) & ChrW(&H644) & ChrW(&H64A) & " " & ChrW(&H644) & ChrW(&H644) & ChrW(&H645) & ChrW(&H643) & ChrW(&H627) & ChrW(&H641) & ChrW(&H622) & ChrW(&H62A) & ": " & Format(totalResult + resultValue, "$#,##0.00") & vbCrLf & vbCrLf
    Else
        totalFormatted = vbCrLf
    End If
    
    If IsNumeric(Me.TextBox11.Value) Then
        volumeValue = Round(CDbl(Me.TextBox11.Value), 2)
    ElseIf InStr(Me.TextBox11.Value, "Lot") > 0 Then
        volumeValue = Round(CDbl(Replace(Me.TextBox11.Value, "Lot", "")), 2)
    Else
        volumeValue = 0
    End If
    volumeFormatted = ChrW(&H62D) & ChrW(&H62C) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62A) & ChrW(&H62F) & ChrW(&H627) & ChrW(&H648) & ChrW(&H644) & " " & ChrW(&H62A) & ChrW(&H62D) & ChrW(&H62A) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & " " & ChrW(&H62E) & ChrW(&H644) & ChrW(&H627) & ChrW(&H644) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H634) & ChrW(&H647) & ChrW(&H631) & ": " & volumeValue & " Lot" & vbCrLf & vbCrLf

    reportText = _
        ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H64A) & ChrW(&H644) & ":" & vbCrLf & _
        ChrW(&H646) & ChrW(&H648) & ChrW(&H639) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & ": " & Me.ComboBox2.Value & vbCrLf & _
        ChrW(&H645) & ChrW(&H631) & ChrW(&H62A) & ChrW(&H628) & ChrW(&H629) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H64A) & ChrW(&H644) & ": " & Me.ComboBox1.Value & vbCrLf & _
        ChrW(&H631) & ChrW(&H642) & ChrW(&H645) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H648) & ChrW(&H643) & ChrW(&H627) & ChrW(&H644) & ChrW(&H629) & ": " & Me.ComboBox6.Value & vbCrLf & _
        volumeFormatted & _
        numberedBonuses & _
        totalFormatted & _
        cleanedText & vbCrLf & _
        ChrW(&H625) & ChrW(&H630) & ChrW(&H627) & " " & ChrW(&H623) & ChrW(&H645) & ChrW(&H643) & ChrW(&H646) & " " & ChrW(&H625) & ChrW(&H636) & ChrW(&H627) & ChrW(&H641) & ChrW(&H629) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62C) & ChrW(&H627) & ChrW(&H626) & ChrW(&H632) & ChrW(&H629) & " " & ChrW(&H644) & ChrW(&H637) & ChrW(&H641) & ChrW(&H627) & vbCrLf & "@AhmedMousa27"
    
    With New MSForms.DataObject
        .SetText reportText
        .PutInClipboard
    End With

    ShowArabicMessage ChrW(&H62A) & ChrW(&H645) & " " & ChrW(&H646) & ChrW(&H633) & ChrW(&H62E) & " " & ChrW(&H627) & ChrW(&H644) & ChrW(&H62A) & ChrW(&H642) & ChrW(&H631) & ChrW(&H64A) & ChrW(&H631) & " " & ChrW(&H628) & ChrW(&H646) & ChrW(&H62C) & ChrW(&H627) & ChrW(&H62D) & ".", ChrW(&H62A) & ChrW(&H645) & " " & ChrW(&H628) & ChrW(&H646) & ChrW(&H62C) & ChrW(&H627) & ChrW(&H62D), 64
