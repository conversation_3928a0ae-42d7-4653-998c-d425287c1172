Private Sub ComboBox1_Change()
    On Error Resume Next
    Me.TextBox10.Value = ""
    Me.TextBox12.Value = ""
    Me.TextBox13.Value = ""
    Me.TextBox8.Value = ""
    
    Select Case Me.ComboBox1.Value
        Case "Bronze":   Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B2").Value, "$#,##0.00")
        Case "Silver":   Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B3").Value, "$#,##0.00")
        Case "Gold":     Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B4").Value, "$#,##0.00")
        Case "Platinum": Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B5").Value, "$#,##0.00")
        Case "Diamond":  Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B6").Value, "$#,##0.00")
        Case "Sapphire": Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B7").Value, "$#,##0.00")
        Case "Emerald":  Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B8").Value, "$#,##0.00")
        Case "king":     Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B9").Value, "$#,##0.00")
        Case "The Legend": Me.TextBox10.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("B10").Value, "$#,##0.00")
        
        Case "Beginning": Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E2").Value, "$#,##0.00")
        Case "Growth":    Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E3").Value, "$#,##0.00")
        Case "Pro"
            Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E4").Value, "$#,##0.00")
            Me.TextBox13.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E11").Value, "$#,##0.00")
        Case "Elite"
            Me.TextBox12.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E5").Value, "$#,##0.00")
            Me.TextBox13.Value = Format(ThisWorkbook.Sheets("Sheet1").Range("E12").Value, "$#,##0.00")
    End Select
    
    Select Case Me.ComboBox1.Value
        Case "Pro", "Elite"
            Me.TextBox13.Visible = True
            Me.Label11.Visible = True
        Case Else
            Me.TextBox13.Visible = False
            Me.Label11.Visible = False
    End Select
    
    If Me.ComboBox1.Value = "Elite" Then
        Me.Label12.Visible = True
        Me.ComboBox3.Visible = True
        Me.ComboBox3.Clear
        Me.ComboBox3.AddItem ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 1"
        Me.ComboBox3.AddItem ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 2"
        Me.ComboBox3.AddItem ChrW(&H645) & ChrW(&H633) & ChrW(&H62A) & ChrW(&H648) & ChrW(&H649) & " 3"
    Else
        Me.Label12.Visible = False
        Me.ComboBox3.Visible = False
    End If
    
    On Error GoTo 0
End Sub

Private Sub ComboBox2_Change()
    On Error Resume Next
    Dim i As Long
    Dim optionsRegular As Variant
    Dim optionsExclusive As Variant
    optionsRegular = Array("Beginning", "Growth", "Pro", "Elite")
    optionsExclusive = Array("Bronze", "Silver", "Gold", "Platinum", "Diamond", "Sapphire", "Emerald", "king", "The Legend")
    ComboBox1.Clear
    Me.TextBox10.Value = ""
    Me.TextBox12.Value = ""
    Me.TextBox13.Value = ""
    Me.TextBox8.Value = ""
    
    ' تحويل النص إلى أحرف صغيرة للمقارنة
    Dim comboValue As String
    comboValue = LCase(Trim(ComboBox2.Value))
    
    Select Case comboValue
        Case ChrW(&H62D) & ChrW(&H635) & ChrW(&H631) & ChrW(&H64A), "حصري", "exclusive"
            For i = LBound(optionsExclusive) To UBound(optionsExclusive)
                ComboBox1.AddItem optionsExclusive(i)
            Next i
            ComboBox1.ListIndex = 0
            Label5.Visible = True
            TextBox10.Visible = True
            Label4.Visible = True
            TextBox9.Visible = True
            CommandButton1.Visible = True
            Label6.Visible = True
            TextBox11.Visible = True
            Label9.Visible = False
            TextBox12.Visible = False
            CommandButton2.Visible = False
            Label10.Visible = False
            TextBox8.Visible = False
            
        Case ChrW(&H625) & ChrW(&H639) & ChrW(&H62A) & ChrW(&H64A) & ChrW(&H627) & ChrW(&H62F) & ChrW(&H64A), "إعتيادي", "regular"
            For i = LBound(optionsRegular) To UBound(optionsRegular)
                ComboBox1.AddItem optionsRegular(i)
            Next i
            ComboBox1.ListIndex = 0
            TextBox11.Visible = True
            Label9.Visible = True
            TextBox12.Visible = True
            CommandButton2.Visible = True
            Label10.Visible = True
            TextBox8.Visible = True
            Label5.Visible = False
            TextBox10.Visible = False
            Label4.Visible = False
            TextBox9.Visible = False
            CommandButton1.Visible = False
            Label6.Visible = True
            TextBox10.Visible = False
            
        Case Else
            ComboBox1.Clear
            Label5.Visible = False
            TextBox10.Visible = False
            Label4.Visible = False
            TextBox9.Visible = False
            CommandButton1.Visible = False
            Label6.Visible = False
            TextBox11.Visible = False
            Label9.Visible = False
            TextBox12.Visible = False
            TextBox8.Visible = False
            CommandButton2.Visible = False
            Label10.Visible = False
            TextBox13.Visible = False
            Label12.Visible = False
    End Select
    
    On Error GoTo 0
End Sub
